package com.cmcc.cmdevops.ci.service.business.impl.buildStrategy;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cmcc.cmdevops.ci.service.atom.BuildToolAtomService;
import com.cmcc.cmdevops.ci.service.bo.BuildSnapshotBO;
import com.cmcc.cmdevops.ci.service.bo.BuildStepConfigBO;
import com.cmcc.cmdevops.ci.service.bo.BuildStepsCmakeBO;
import com.cmcc.cmdevops.ci.service.bo.BuildToolBO;
import com.cmcc.cmdevops.ci.service.business.BuildStrategy;
import com.cmcc.cmdevops.ci.service.business.util.EmptyValidator;
import com.cmcc.cmdevops.ci.service.business.util.SpringUtils;
import com.cmcc.cmdevops.ci.service.dao.BuildToolDO;
import com.cmcc.cmdevops.exception.BusinessException;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class CmakeBuildStrategy implements BuildStrategy {
    @Override
    public String stageShell(BuildSnapshotBO buildSnapshotBO, BuildStepConfigBO buildStepsBO, List<BuildToolBO> buildToolBOList) {
        StringBuilder buildShell = new StringBuilder();
        BuildStepsCmakeBO cipBuildStepsCmakeDTO = buildStepsBO.getConfig().toJavaObject(BuildStepsCmakeBO.class);
        BuildToolBO buildToolBO = null;
        Optional<BuildToolBO> toolBOOptional = buildToolBOList.stream()
                .filter(buildTool -> buildTool.getToolName().equals(cipBuildStepsCmakeDTO.getToolVersion()))
                .findAny();
        if (toolBOOptional.isPresent()) {
            buildToolBO = toolBOOptional.get();
        }

        // 判断是否开启了分布式构建,如果是，先启动icecream相关容器
        if (cipBuildStepsCmakeDTO.getEnableDistributedBuild()) {
            // 获取icecream镜像名称
            String icecreamImage = getIcecreamImage(buildToolBOList);
            // 添加icecream分布式集群环境初始化stage
            buildShell.append(generateIcecreamInitStage(buildSnapshotBO, icecreamImage));
        }

        // 配置构建工具参数
        if (EmptyValidator.isNotEmpty(buildToolBO)) {
            String args = " -v ${WORKSPACE}:/workspace -w /workspace";
            if (cipBuildStepsCmakeDTO.getEnableDistributedBuild()) {
                args += " --network host";
                args += " -e USE_DISTRIBUTED_BUILD=true";
            } else {
                args += " -e USE_DISTRIBUTED_BUILD=false";
            }
            buildToolBO.setArgs(buildToolBO.getArgs() + args);
        }

        // 生成CMake构建stage
        buildShell.append("stage('").append(buildStepsBO.getName()).append("-").append(buildStepsBO.getSerial()).append("') { \n")
                .append(JenkinsUtils.getAgent(buildToolBO))
                .append("steps { \n")
                .append("script { \n");

        if (cipBuildStepsCmakeDTO.getEnableDistributedBuild()) {
            buildShell.append(generateDistributedBuildScript(cipBuildStepsCmakeDTO.getCommand()));
        } else {
            buildShell.append("sh '''").append(wrapWithCcache(JenkinsUtils.commandReplace(cipBuildStepsCmakeDTO.getCommand()))).append("'''\n");
        }

        buildShell.append("} \n")
                .append("} \n")
                .append("} \n");
        return buildShell.toString();
    }

    private String wrapWithCcache(String userCommand) {
        return "/usr/local/bin/jenkins-build-init.sh \"" + JenkinsUtils.commandReplace(userCommand) + "\"\n";
    }

    /**
     * 获取icecream镜像名称
     */
    private String getIcecreamImage(List<BuildToolBO> buildToolBOList) {
        // 查找icecream相关的构建工具
        BuildToolAtomService buildToolAtomService = SpringUtils.getBean(BuildToolAtomService.class);
        LambdaQueryWrapper<BuildToolDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BuildToolDO::getToolName, "icecream-centos7:20250930");
        BuildToolDO buildToolDO = buildToolAtomService.getOne(queryWrapper);
        if (buildToolDO != null) {
            return buildToolDO.getToolImage();
        }
        // 如果没找到专门的icecream工具，使用默认镜像
        throw new BusinessException("未获取到icecream镜像，无法完成分布式构建");
    }

    /**
     * 生成icecream分布式集群环境初始化stage
     */
    private String generateIcecreamInitStage(BuildSnapshotBO buildSnapshotBO, String icecreamImage) {
        StringBuilder stage = new StringBuilder();

        stage.append("stage('icecream分布式集群环境初始化') { \n")
                .append("steps { \n")
                .append("script {\n")
                .append("echo '生成Docker Compose配置文件...'\n")
                .append("// 获取宿主机IP地址 - 兼容BusyBox环境\n")
                .append("def hostIP = \"\"\n")
                .append("try {\n")
                .append("// 方法1: 尝试使用hostname -I (如果支持)\n")
                .append("hostIP = sh(script: \"hostname -I 2>/dev/null | awk '{print \\$1}' || echo ''\", returnStdout: true).trim()\n")
                .append("if (hostIP == \"\") {\n")
                .append("// 方法2: 使用ip命令获取默认路由接口的IP (BusyBox兼容)\n")
                .append("hostIP = sh(script: \"ip route get ******* 2>/dev/null | grep 'src' | awk '{for(i=1;i<=NF;i++) if(\\$i==\\\"src\\\") print \\$(i+1)}' | head -1 || echo ''\", returnStdout: true).trim()\n")
                .append("}\n")
                .append("if (hostIP == \"\") {\n")
                .append("// 方法3: 使用ifconfig获取第一个非回环接口的IP\n")
                .append("hostIP = sh(script: \"ifconfig 2>/dev/null | grep 'inet ' | grep -v '127.0.0.1' | head -1 | awk '{print \\$2}' | cut -d: -f2 || echo ''\", returnStdout: true).trim()\n")
                .append("}\n")
                .append("if (hostIP == \"\") {\n")
                .append("// 方法4: 从/proc/net/route获取默认网关接口，然后获取IP\n")
                .append("hostIP = sh(script: \"\"\"\n")
                .append("DEFAULT_IFACE=\\$(awk '\\$2 == \\\"00000000\\\" { print \\$1; exit }' /proc/net/route 2>/dev/null)\n")
                .append("if [ -n \\\"\\$DEFAULT_IFACE\\\" ]; then\n")
                .append("ip addr show \\$DEFAULT_IFACE 2>/dev/null | grep 'inet ' | head -1 | awk '{print \\$2}' | cut -d/ -f1 || echo ''\n")
                .append("else\n")
                .append("echo ''\n")
                .append("fi\n")
                .append("\"\"\", returnStdout: true).trim()\n")
                .append("}\n")
                .append("if (hostIP == \"\") {\n")
                .append("// 方法5: 最后的回退方案，使用容器内部可见的网关IP\n")
                .append("hostIP = sh(script: \"route -n 2>/dev/null | awk '\\$1==\\\"0.0.0.0\\\" {print \\$2; exit}' || echo '**********'\", returnStdout: true).trim()\n")
                .append("}\n")
                .append("} catch (Exception e) {\n")
                .append("echo \"获取IP地址时发生异常: ${e.getMessage()}\"\n")
                .append("hostIP = \"**********\"  // 默认Docker网关IP\n")
                .append("}\n\n")
                .append("// 验证IP地址格式\n")
                .append("if (!hostIP.matches(/^\\d+\\.\\d+\\.\\d+\\.\\d+$/)) {\n")
                .append("echo \"警告: 获取到的IP地址格式不正确: '${hostIP}'\"\n")
                .append("hostIP = \"**********\"  // 使用默认值\n")
                .append("}\n\n")
                .append("echo \"检测到宿主机IP: ${hostIP}\"\n\n");

        // 生成Docker Compose配置
        stage.append("// 生成简化的Docker Compose配置\n")
                .append("def currentTime = new Date().toString()\n")
                .append("def composeContent = \"\"\"# icecream分布式编译环境配置\n")
                .append("# 生成时间: ${currentTime}\n")
                .append("# 主机IP: \\${hostIP}\n")
                .append("# 网络模式: host\n\n")
                .append("services:\n")
                .append("  # icecream调度器节点\n")
                .append("  icecc-scheduler:\n")
                .append("    image: ").append(icecreamImage).append("\n")
                .append("    container_name: icecc-scheduler\n")
                .append("    hostname: icecc-scheduler\n")
                .append("    network_mode: host\n")
                .append("    environment:\n")
                .append("      - HOST_IP=\\${hostIP}\n")
                .append("    command: |\n")
                .append("      icecc-scheduler -u nobody -p 8765 -l /tmp/icecc-scheduler.log -vvv --persistent-client-connection\n")
                .append("    restart: unless-stopped\n\n")
                .append("  # icecream工作节点1 - 启动iceccd守护进程，使用不同端口避免冲突\n")
                .append("  icecc-worker-1:\n")
                .append("    image: ").append(icecreamImage).append("\n")
                .append("    container_name: icecc-worker-1\n")
                .append("    hostname: icecc-worker-1\n")
                .append("    network_mode: host\n")
                .append("    depends_on:\n")
                .append("      - icecc-scheduler\n")
                .append("    environment:\n")
                .append("      - HOST_IP=\\${hostIP}\n")
                .append("      - SCHEDULER_ADDR=\\${hostIP}:8765\n")
                .append("    command: |\n")
                .append("      sh -c '\n")
                .append("        for i in $(seq 1 30); do\n")
                .append("          if timeout 3 bash -c \"</dev/tcp/\\${hostIP}/8765\" 2>/dev/null; then\n")
                .append("            echo \"Scheduler is ready!\"\n")
                .append("            break\n")
                .append("          fi\n")
                .append("          echo \"Attempt $i/30: Scheduler not ready, waiting...\"\n")
                .append("          sleep 2\n")
                .append("        done\n")
                .append("        iceccd -u nobody -d -s \\${hostIP}:8765 -p 10245 -l /tmp/icecc-worker1.log -vvv\n")
                .append("        tail -f /dev/null\n")
                .append("      '\n")
                .append("    restart: unless-stopped\n\n")
                .append("  # icecream工作节点2 - 启动iceccd守护进程，使用不同端口避免冲突\n")
                .append("  icecc-worker-2:\n")
                .append("    image: ").append(icecreamImage).append("\n")
                .append("    container_name: icecc-worker-2\n")
                .append("    hostname: icecc-worker-2\n")
                .append("    network_mode: host\n")
                .append("    depends_on:\n")
                .append("      - icecc-scheduler\n")
                .append("    environment:\n")
                .append("      - HOST_IP=\\${hostIP}\n")
                .append("      - SCHEDULER_ADDR=\\${hostIP}:8765\n")
                .append("    command: |\n")
                .append("      sh -c '\n")
                .append("        for i in $(seq 1 30); do\n")
                .append("          if timeout 3 bash -c \"</dev/tcp/\\${hostIP}/8765\" 2>/dev/null; then\n")
                .append("            echo \"Scheduler is ready!\"\n")
                .append("            break\n")
                .append("          fi\n")
                .append("          echo \"Attempt $i/30: Scheduler not ready, waiting...\"\n")
                .append("          sleep 2\n")
                .append("        done\n")
                .append("        sleep 3\n")
                .append("        iceccd -u nobody -d -s \\${hostIP}:8765 -p 10246 -l /tmp/icecc-worker2.log -vvv\n")
                .append("        tail -f /dev/null\n")
                .append("      '\n")
                .append("    restart: unless-stopped\"\"\"\n\n")
                .append("writeFile encoding: 'UTF-8', file: './tmp/docker-compose.yml', text: composeContent\n\n");

        // 添加验证和启动逻辑
        stage.append("// 验证IP地址有效性\n")
                .append("if (hostIP == \"\" || hostIP == \"**********\") {\n")
                .append("echo \"⚠ 警告: 使用默认或回退IP地址: ${hostIP}\"\n")
                .append("echo \"这可能导致网络连接问题，请检查网络配置\"\n")
                .append("}\n\n")
                .append("echo '开始执行Docker Compose操作...'\n")
                .append("sh '''docker-compose -f ./tmp/docker-compose.yml up -d'''\n")
                .append("echo 'Docker Compose操作完成'\n\n")
                .append("// 等待调度器启动并验证连接\n")
                .append("echo '等待调度器启动...'\n")
                .append("sleep 10  // 适度等待时间\n\n")
                .append("// 检查容器状态\n")
                .append("echo \"检查icecream容器状态:\"\n")
                .append("sh '''\n")
                .append("echo \"=== Docker容器状态 ===\"\n")
                .append("docker ps | grep icecc || echo \"无icecream容器运行\"\n")
                .append("echo \"\"\n\n")
                .append("echo \"=== 调度器容器日志 (最新10行) ===\"\n")
                .append("docker logs --tail 10 icecc-scheduler 2>/dev/null || echo \"无法获取调度器日志\"\n")
                .append("echo \"\"\n")
                .append("'''\n\n")
                .append("// 验证调度器是否正常启动\n")
                .append("def schedulerStatus = sh(script: \"netstat -ln | grep :8765 || echo 'NOT_FOUND'\", returnStdout: true).trim()\n")
                .append("if (schedulerStatus.contains('8765')) {\n")
                .append("echo \"✓ 调度器已成功启动并监听端口8765\"\n\n")
                .append("def connectionSuccess = false\n")
                .append("for (int attempt = 1; attempt <= 5; attempt++) {\n")
                .append("echo \"调度器连接测试 (第${attempt}/5次)...\"\n")
                .append("def connectionTest = sh(script: \"timeout 5 bash -c '</dev/tcp/${hostIP}/8765' 2>/dev/null && echo 'CONNECTED' || echo 'FAILED'\", returnStdout: true).trim()\n")
                .append("if (connectionTest == 'CONNECTED') {\n")
                .append("echo \"✓ 调度器连接测试成功\"\n")
                .append("connectionSuccess = true\n")
                .append("break\n")
                .append("} else {\n")
                .append("echo \"⚠ 调度器连接测试失败，等待3秒后重试...\"\n")
                .append("sleep 3\n")
                .append("}\n")
                .append("}\n\n")
                .append("if (!connectionSuccess) {\n")
                .append("echo \"❌ 调度器连接测试最终失败\"\n")
                .append("}\n")
                .append("} else {\n")
                .append("echo \"⚠ 警告: 调度器可能未正常启动\"\n\n")
                .append("// 显示详细的网络诊断信息\n")
                .append("echo \"网络诊断信息:\"\n")
                .append("sh \"\"\"\n")
                .append("echo \\\"主机IP: ${hostIP}\\\"\n")
                .append("echo \\\"监听端口:\\\"\n")
                .append("netstat -ln | grep :8765 || echo \\\"端口8765未监听\\\"\n")
                .append("echo \\\"\\\"\n")
                .append("echo \\\"所有监听端口:\\\"\n")
                .append("netstat -ln | head -10\n")
                .append("echo \\\"\\\"\n")
                .append("echo \\\"Docker容器详细状态:\\\"\n")
                .append("docker ps -a | grep icecc || echo \\\"无icecream容器\\\"\n")
                .append("echo \\\"\\\"\n")
                .append("echo \\\"调度器容器详细日志:\\\"\n")
                .append("docker logs icecc-scheduler 2>/dev/null || echo \\\"无法获取调度器日志\\\"\n")
                .append("\"\"\"\n")
                .append("}\n")
                .append("} \n")
                .append("} \n")
                .append("} \n");

        return stage.toString();
    }

    /**
     * 生成分布式构建脚本
     */
    private String generateDistributedBuildScript(String userCommand) {
        StringBuilder script = new StringBuilder();

        script.append("// 获取宿主机IP地址\n")
                .append("def hostIP = sh(script: \"hostname -I | awk '{print \\$1}'\", returnStdout: true).trim()\n")
                .append("echo \"使用调度器地址: ${hostIP}:8765\"\n\n")
                .append("// 设置环境变量并执行构建 - 让jenkins-build-init.sh自动处理分布式编译配置\n")
                .append("withEnv([\n")
                .append("\"USE_DISTRIBUTED_BUILD=true\",\n")
                .append("\"ICECC_SCHEDULER=${hostIP}:8765\"\n")
                .append("]) {\n")
                .append("sh '''#!/bin/bash\n")
                .append("set -e\n")
                .append("echo \"=== 构建环境信息 ===\"\n")
                .append("echo \"USE_DISTRIBUTED_BUILD: \\$USE_DISTRIBUTED_BUILD\"\n")
                .append("echo \"ICECC_SCHEDULER: \\$ICECC_SCHEDULER\"\n")
                .append("echo \"\"\n\n")
                .append("# 使用jenkins-build-init.sh执行完整的构建流程\n")
                .append("echo \"=== 使用jenkins-build-init.sh执行构建 ===\"\n")
                .append("/usr/local/bin/jenkins-build-init.sh \"").append(JenkinsUtils.commandReplace(userCommand)).append("\"\n")
                .append("'''\n")
                .append("}\n");

        return script.toString();
    }
}
